import { useEffect, useCallback } from "react";
import useRexor from "./useRexor";
import useSystemSettingsStore from "@/stores/settingsStore";
import System from "@/models/system";
import {
  extractTotalHours,
  storeEstimationResult,
} from "@/utils/manualWorkEstimation";
import { ASSISTANT_MESSAGE_COMPLETE_EVENT } from "@/components/contexts/TTSProvider";

/**
 * Hook that automatically handles manual work estimation and Rexor integration
 * when an assistant message is complete, if both invoice logging and Rexor linkage are active.
 */
export default function useAutoRexorIntegration() {
  const { writeOrUpdateTransaction, isLoggedIn } = useRexor();
  const { getSetting } = useSystemSettingsStore();

  const handleAutomaticEstimationAndRexor = useCallback(
    async (event) => {
      try {
        // Check if both invoice logging and Rexor linkage are active
        const invoiceLogging = getSetting("invoice-logging");
        const rexorLinkage = getSetting("rexor-linkage");

        if (!invoiceLogging || !rexorLinkage || !isLoggedIn) {
          return;
        }

        // Check if there's a registered Rexor project
        const projectDetailsString = localStorage.getItem(
          "rexor_registered_project"
        );
        if (!projectDetailsString) {
          return;
        }

        const { chatId } = event.detail;
        if (!chatId) {
          return;
        }

        // Get the current thread slug from URL
        const currentPath = window.location.pathname;
        const threadSlugMatch = currentPath.match(
          /\/workspace\/([^\/]+)\/thread\/([^\/]+)/
        );

        if (!threadSlugMatch) {
          return;
        }

        const threadSlug = threadSlugMatch[2];

        // Get the chat history to find the question and answer
        const chatHistory = await getChatHistoryForMessage(chatId);
        if (!chatHistory || !chatHistory.question || !chatHistory.answer) {
          console.log(
            "Could not find question/answer for automatic estimation"
          );
          // Proceed with Rexor transaction without estimation
          await writeOrUpdateTransaction();
          return;
        }

        console.log("Starting automatic manual work estimation...");

        // Generate manual work estimation in the background
        try {
          const estimationResponse = await System.estimateManualWork({
            question: chatHistory.question,
            answer: chatHistory.answer,
          });

          if (estimationResponse.success && estimationResponse.result) {
            // Extract total hours and store the estimation result
            const totalHours = extractTotalHours(
              estimationResponse.result.textResponse
            );

            if (totalHours && totalHours > 0) {
              storeEstimationResult(
                threadSlug,
                chatId,
                estimationResponse.result.textResponse,
                totalHours
              );
              console.log(
                `Automatic estimation completed: ${totalHours} hours`
              );
            }
          }
        } catch (estimationError) {
          console.warn(
            "Automatic manual work estimation failed:",
            estimationError
          );
          // Continue with Rexor transaction even if estimation fails
        }

        // Trigger Rexor transaction (will include estimation if available)
        await writeOrUpdateTransaction();
        console.log("Automatic Rexor transaction completed");
      } catch (error) {
        console.error("Error in automatic Rexor integration:", error);
        // Fallback: try Rexor transaction without estimation
        try {
          await writeOrUpdateTransaction();
        } catch (fallbackError) {
          console.error(
            "Fallback Rexor transaction also failed:",
            fallbackError
          );
        }
      }
    },
    [getSetting, isLoggedIn, writeOrUpdateTransaction]
  );

  useEffect(() => {
    if (typeof window === "undefined") return;

    window.addEventListener(
      ASSISTANT_MESSAGE_COMPLETE_EVENT,
      handleAutomaticEstimationAndRexor
    );

    return () => {
      window.removeEventListener(
        ASSISTANT_MESSAGE_COMPLETE_EVENT,
        handleAutomaticEstimationAndRexor
      );
    };
  }, [handleAutomaticEstimationAndRexor]);
}

/**
 * Helper function to get chat history for a specific message
 * This tries multiple approaches to find the question and answer
 */
async function getChatHistoryForMessage(chatId) {
  try {
    // Method 1: Try to get from the current chat history in the DOM
    // Look for the chat history container
    const chatHistoryContainer = document.getElementById("chat-history");
    if (chatHistoryContainer) {
      // Find all message elements
      const messageElements = chatHistoryContainer.querySelectorAll(
        "[data-role], .group"
      );

      let question = "";
      let answer = "";
      let foundTargetMessage = false;

      // Look through messages to find the one with matching chatId
      for (let i = 0; i < messageElements.length; i++) {
        const element = messageElements[i];

        // Check if this element contains our target chatId
        const chatIdElements = element.querySelectorAll("[data-chat-id]");
        const hasTargetChatId = Array.from(chatIdElements).some(
          (el) => el.getAttribute("data-chat-id") === chatId
        );

        if (hasTargetChatId) {
          foundTargetMessage = true;

          // This should be the assistant message, get its content
          answer = element.textContent || element.innerText || "";

          // Look backwards for the user message
          for (let j = i - 1; j >= 0; j--) {
            const prevElement = messageElements[j];
            const prevText =
              prevElement.textContent || prevElement.innerText || "";

            // Check if this looks like a user message (shorter, no citations, etc.)
            if (
              prevText &&
              prevText.length > 0 &&
              prevText.length < answer.length * 2
            ) {
              question = prevText;
              break;
            }
          }
          break;
        }
      }

      if (foundTargetMessage && question && answer) {
        return { question: question.trim(), answer: answer.trim() };
      }
    }

    // Method 2: Try to get from React component state (if available)
    // This is a fallback method that looks for React fiber data
    const reactFiberKey = Object.keys(document.body).find(
      (key) =>
        key.startsWith("__reactFiber") ||
        key.startsWith("__reactInternalInstance")
    );

    if (reactFiberKey) {
      // Try to traverse React fiber to find chat history
      // This is a more advanced approach but might work
      try {
        const fiber = document.body[reactFiberKey];
        // This would require more complex traversal logic
        // For now, we'll skip this approach
      } catch (error) {
        console.debug("Could not access React fiber data:", error);
      }
    }

    // Method 3: Simple DOM text extraction as fallback
    // Get the last user message and assistant message from the page
    const allText = document.body.textContent || document.body.innerText || "";

    // This is a very basic fallback - look for patterns that might indicate Q&A
    const lines = allText.split("\n").filter((line) => line.trim().length > 10);

    if (lines.length >= 2) {
      // Take the last two substantial lines as question and answer
      const potentialAnswer = lines[lines.length - 1].trim();
      const potentialQuestion = lines[lines.length - 2].trim();

      // Basic validation - answer should be longer than question typically
      if (
        potentialAnswer.length > potentialQuestion.length &&
        potentialQuestion.length > 10 &&
        potentialAnswer.length > 20
      ) {
        return {
          question: potentialQuestion,
          answer: potentialAnswer,
        };
      }
    }

    return null;
  } catch (error) {
    console.error("Error getting chat history for message:", error);
    return null;
  }
}
