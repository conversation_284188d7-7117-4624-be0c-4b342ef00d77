import { useEffect, useCallback } from "react";
import useRexor from "./useRexor";
import useSystemSettingsStore from "@/stores/settingsStore";
import System from "@/models/system";
import {
  extractTotalHours,
  storeEstimationResult,
} from "@/utils/manualWorkEstimation";
import { ASSISTANT_MESSAGE_COMPLETE_EVENT } from "@/components/contexts/TTSProvider";

/**
 * Hook that automatically handles manual work estimation and Rexor integration
 * when an assistant message is complete, if both invoice logging and Rexor linkage are active.
 */
export default function useAutoRexorIntegration() {
  const { writeOrUpdateTransaction, isLoggedIn } = useRexor();
  const { getSetting } = useSystemSettingsStore();

  const handleAutomaticEstimationAndRexor = useCallback(
    async (event) => {
      try {
        // Check if both invoice logging and Rexor linkage are active
        const invoiceLogging = getSetting("invoice-logging");
        const rexorLinkage = getSetting("rexor-linkage");

        if (!invoiceLogging || !rexorLinkage || !isLoggedIn) {
          return;
        }

        // Check if there's a registered Rexor project
        const projectDetailsString = localStorage.getItem(
          "rexor_registered_project"
        );
        if (!projectDetailsString) {
          return;
        }

        const { chatId, question, answer } = event.detail;
        if (!chatId) {
          return;
        }

        // Get the current thread slug from URL
        const currentPath = window.location.pathname;
        const threadSlugMatch = currentPath.match(
          /\/workspace\/([^\/]+)\/thread\/([^\/]+)/
        );

        if (!threadSlugMatch) {
          return;
        }

        const threadSlug = threadSlugMatch[2];

        // Check if we have question and answer from the event
        if (!question || !answer) {
          console.log(
            "Could not find question/answer for automatic estimation"
          );
          // Proceed with Rexor transaction without estimation
          await writeOrUpdateTransaction();
          return;
        }

        console.log("Starting automatic manual work estimation...");

        // Generate manual work estimation in the background
        try {
          const estimationResponse = await System.estimateManualWork({
            question: question,
            answer: answer,
          });

          if (estimationResponse.success && estimationResponse.result) {
            // Extract total hours and store the estimation result
            const totalHours = extractTotalHours(
              estimationResponse.result.textResponse
            );

            if (totalHours && totalHours > 0) {
              storeEstimationResult(
                threadSlug,
                chatId,
                estimationResponse.result.textResponse,
                totalHours
              );
              console.log(
                `Automatic estimation completed: ${totalHours} hours`
              );
            }
          }
        } catch (estimationError) {
          console.warn(
            "Automatic manual work estimation failed:",
            estimationError
          );
          // Continue with Rexor transaction even if estimation fails
        }

        // Trigger Rexor transaction (will include estimation if available)
        await writeOrUpdateTransaction();
        console.log("Automatic Rexor transaction completed");
      } catch (error) {
        console.error("Error in automatic Rexor integration:", error);
        // Fallback: try Rexor transaction without estimation
        try {
          await writeOrUpdateTransaction();
        } catch (fallbackError) {
          console.error(
            "Fallback Rexor transaction also failed:",
            fallbackError
          );
        }
      }
    },
    [getSetting, isLoggedIn, writeOrUpdateTransaction]
  );

  useEffect(() => {
    if (typeof window === "undefined") return;

    window.addEventListener(
      ASSISTANT_MESSAGE_COMPLETE_EVENT,
      handleAutomaticEstimationAndRexor
    );

    return () => {
      window.removeEventListener(
        ASSISTANT_MESSAGE_COMPLETE_EVENT,
        handleAutomaticEstimationAndRexor
      );
    };
  }, [handleAutomaticEstimationAndRexor]);
}
