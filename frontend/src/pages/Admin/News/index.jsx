import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import Modal from "@/components/ui/Modal";
import Markdown from "@/components/ui/Markdown";
import Input from "@/components/ui/Input";
import Label from "@/components/ui/Label";
import Textarea from "@/components/ui/Textarea";
import FormItem from "@/components/ui/FormItem";
import Tooltip from "@/components/ui/Tooltip";
import {
  Plus,
  PencilSimple,
  Trash,
  Eye,
  ArrowLeft,
} from "@phosphor-icons/react";
import { API_BASE } from "@/utils/constants";
import { baseHeaders } from "@/utils/request";
import showToast from "@/utils/toast";
import SettingsSidebar from "@/components/SettingsSidebar";
import HeaderWorkspace from "@/components/HeaderWorkspace";
import { Link } from "react-router-dom";
import paths from "@/utils/paths";
import {
  getActiveSystemNews,
  resolveSystemNewsTranslations,
} from "@/data/news";

export default function NewsManagement() {
  const { t } = useTranslation();
  const [news, setNews] = useState([]);
  const [systemNews, setSystemNews] = useState([]);
  const [allNews, setAllNews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingNews, setEditingNews] = useState(null);
  const [viewingNews, setViewingNews] = useState(null);
  const [formData, setFormData] = useState({
    title: "",
    content: "",
    priority: "medium",
    targetRoles: [],
    expiresAt: "",
  });

  // Load system news on component mount
  useEffect(() => {
    const loadSystemNews = () => {
      try {
        // Get active system news and resolve translations
        const activeSystemNews = getActiveSystemNews(t);
        setSystemNews(activeSystemNews);
      } catch (error) {
        console.error("Error loading system news:", error);
        showToast(t("news-system.error.fetch"), "error");
      }
    };

    loadSystemNews();
  }, [t]);

  // Fetch database news
  const fetchNews = async (signal) => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE}/admin/news`, {
        headers: baseHeaders(),
        signal,
      });

      if (!response.ok) {
        throw new Error("Failed to fetch news");
      }

      const data = await response.json();
      setNews(data.news || []);
    } catch (error) {
      // Don't show error if request was aborted
      if (error.name === "AbortError") {
        return;
      }
      console.error("Error fetching news:", error);
      showToast(t("news-system.management.messages.fetchError"), "error");
    } finally {
      setLoading(false);
    }
  };

  // Combine and sort all news (system + database)
  useEffect(() => {
    const combined = [
      ...systemNews.map((item) => ({ ...item, isSystemNews: true })),
      ...news.map((item) => ({ ...item, isSystemNews: false })),
    ];

    // Sort by priority (urgent > high > medium > low) then by creation date
    const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
    combined.sort((a, b) => {
      const priorityDiff =
        (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
      if (priorityDiff !== 0) return priorityDiff;

      return new Date(b.createdAt) - new Date(a.createdAt);
    });

    setAllNews(combined);
  }, [systemNews, news]);

  useEffect(() => {
    const abortController = new AbortController();
    fetchNews(abortController.signal);

    return () => {
      abortController.abort();
    };
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    const abortController = new AbortController();

    try {
      const url = editingNews
        ? `${API_BASE}/admin/news/${editingNews.id}`
        : `${API_BASE}/admin/news`;

      const method = editingNews ? "PUT" : "POST";

      const payload = {
        ...formData,
        targetRoles:
          formData.targetRoles.length > 0 ? formData.targetRoles : null,
        expiresAt: formData.expiresAt || null,
      };

      const response = await fetch(url, {
        method,
        headers: baseHeaders(),
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${editingNews ? "update" : "create"} news`);
      }

      showToast(
        editingNews
          ? t("news-system.management.messages.updateSuccess")
          : t("news-system.management.messages.createSuccess"),
        "success"
      );

      setShowModal(false);
      setEditingNews(null);
      resetForm();
      fetchNews(abortController.signal);
    } catch (error) {
      console.error("Error saving news:", error);
      showToast(
        editingNews
          ? t("news-system.management.messages.updateError")
          : t("news-system.management.messages.createError"),
        "error"
      );
    }
  };

  const handleDelete = async (newsItem) => {
    if (newsItem.isSystemNews) {
      showToast(t("news-system.management.actions.cannotDelete"), "error");
      return;
    }

    if (!window.confirm(t("news-system.management.confirmations.delete"))) {
      return;
    }

    const abortController = new AbortController();

    try {
      const response = await fetch(`${API_BASE}/admin/news/${newsItem.id}`, {
        method: "DELETE",
        headers: baseHeaders(),
      });

      if (!response.ok) {
        throw new Error("Failed to delete news");
      }

      showToast(t("news-system.management.messages.deleteSuccess"), "success");
      fetchNews(abortController.signal);
    } catch (error) {
      console.error("Error deleting news:", error);
      showToast(t("news-system.management.messages.deleteError"), "error");
    }
  };

  const openEditModal = (newsItem) => {
    if (newsItem.isSystemNews) {
      showToast(t("news-system.management.actions.cannotEdit"), "error");
      return;
    }

    setEditingNews(newsItem);
    setFormData({
      title: newsItem.title,
      content: newsItem.content,
      priority: newsItem.priority,
      targetRoles: newsItem.targetRoles || [],
      expiresAt: newsItem.expiresAt
        ? new Date(newsItem.expiresAt).toISOString().slice(0, 16)
        : "",
    });
    setShowModal(true);
  };

  const openViewModal = (newsItem) => {
    // For system news, resolve translations if needed
    if (newsItem.isSystemNews && (newsItem.titleKey || newsItem.contentKey)) {
      const resolvedNews = resolveSystemNewsTranslations(newsItem, t);
      setViewingNews(resolvedNews);
    } else {
      setViewingNews(newsItem);
    }
  };

  const openCreateModal = () => {
    setEditingNews(null);
    resetForm();
    setShowModal(true);
  };

  const resetForm = () => {
    setFormData({
      title: "",
      content: "",
      priority: "medium",
      targetRoles: [],
      expiresAt: "",
    });
  };

  const handleRoleChange = (role, checked) => {
    if (checked) {
      setFormData({
        ...formData,
        targetRoles: [...formData.targetRoles, role],
      });
    } else {
      setFormData({
        ...formData,
        targetRoles: formData.targetRoles.filter((r) => r !== role),
      });
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return t("news-system.management.table.never");
    return new Date(dateString).toLocaleDateString();
  };

  const formatTargetRoles = (roles) => {
    if (!roles || roles.length === 0) {
      return t("news-system.management.table.allUsers");
    }
    return roles
      .map((role) => t(`news-system.management.form.roles.${role}`))
      .join(", ");
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "urgent":
        return "text-red-600 bg-red-100";
      case "high":
        return "text-orange-600 bg-orange-100";
      case "medium":
        return "text-yellow-600 bg-yellow-100";
      case "low":
        return "text-green-600 bg-green-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getSourceBadge = (isSystemNews) => {
    if (isSystemNews) {
      return (
        <Tooltip
          content={t("news-system.management.source.systemTooltip")}
          placement="top"
        >
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {t("news-system.management.table.systemNews")}
          </span>
        </Tooltip>
      );
    } else {
      return (
        <Tooltip
          content={t("news-system.management.source.localTooltip")}
          placement="top"
        >
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            {t("news-system.management.table.localNews")}
          </span>
        </Tooltip>
      );
    }
  };

  // Helper function to translate author names
  const translateAuthor = (author) => {
    if (author === "IST Legal Team") {
      return t("news-system-items.authors.ist-legal-team");
    } else if (author === "System") {
      return t("news-system-items.authors.system");
    }
    return author; // Return original if no translation found
  };

  // Helper function to translate category names
  const translateCategory = (category) => {
    if (category === "welcome") {
      return t("news-system-items.categories.welcome");
    } else if (category === "system-updates") {
      return t("news-system-items.categories.system-updates");
    } else if (category === "general") {
      return t("news-system-items.categories.general");
    }
    return category; // Return original if no translation found
  };

  return (
    <div className="overflow-hidden w-full h-full flex flex-col">
      <HeaderWorkspace />
      <div className="bg-background flex h-[calc(100vh-var(--header-height))]">
        <SettingsSidebar />
        <div className="relative rounded-md w-full h-full overflow-y-scroll">
          <div className="flex flex-col w-full px-1 md:pl-6 md:pr-[50px] md:py-4">
            <div className="w-full flex flex-row items-center gap-3.5 gap-y-1 pb-3 border-bottom">
              <Link to={paths.home()}>
                <ArrowLeft className="h-9 w-9 hover:cursor-pointer p-2 rounded-md text-white primary-bg" />
              </Link>
              <div className="flex flex-col">
                <div className="items-center flex gap-x-4">
                  <p className="text-lg leading-6 font-bold text-foreground">
                    {t("news-system.management.title")}
                  </p>
                </div>
                <p className="text-xs leading-[18px] font-base text-foreground text-opacity-60">
                  {t("news-system.management.description")}
                </p>
              </div>
            </div>
            <div className="w-full justify-end flex absolute right-5">
              <Button onClick={openCreateModal} className="mr-0 -mb-6">
                <Plus className="h-4 w-4" weight="bold" />
                {t("news-system.management.create")}
              </Button>
            </div>

            <div className="mt-6">
              <div className="text-foreground text-opacity-60 text-sm mb-4">
                {t("news-system.management.messages.systemNewsInfo")}
              </div>

              {loading ? (
                <div className="text-center py-8 text-foreground text-opacity-60">
                  {t("news-system.management.loading")}
                </div>
              ) : (
                <div className="bg-white dark:bg-zinc-800 rounded-lg overflow-hidden shadow">
                  <table className="w-full text-sm text-left">
                    <thead className="text-foreground text-opacity-80 text-xs leading-[18px] font-bold uppercase border-white border-b border-opacity-60">
                      <tr>
                        <th className="px-6 py-3 rounded-tl-lg">
                          {t("news-system.management.table.title")}
                        </th>
                        <th className="px-6 py-3">
                          {t("news-system.management.table.priority")}
                        </th>
                        <th className="px-6 py-3">
                          {t("news-system.management.table.targetRoles")}
                        </th>
                        <th className="px-6 py-3">
                          {t("news-system.management.table.created")}
                        </th>
                        <th className="px-6 py-3">
                          {t("news-system.management.table.expires")}
                        </th>
                        <th className="px-6 py-3">
                          {t("news-system.management.table.status")}
                        </th>
                        <th className="px-6 py-3">
                          {t("news-system.management.table.source")}
                        </th>
                        <th className="px-6 py-3 rounded-tr-lg">
                          {t("news-system.management.table.actions")}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="text-foreground">
                      {allNews.map((item) => (
                        <tr
                          key={`${item.isSystemNews ? "system" : "local"}-${item.id}`}
                          className="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"
                        >
                          <td className="px-6 py-4 font-medium">
                            {item.title}
                          </td>
                          <td className="px-6 py-4">
                            <span
                              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(
                                item.priority
                              )}`}
                            >
                              {t(
                                `news-system.management.form.priorities.${item.priority}`
                              )}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-foreground text-opacity-80">
                            {formatTargetRoles(item.targetRoles)}
                          </td>
                          <td className="px-6 py-4 text-foreground text-opacity-80">
                            {formatDate(item.createdAt)}
                          </td>
                          <td className="px-6 py-4 text-foreground text-opacity-80">
                            {formatDate(item.expiresAt)}
                          </td>
                          <td className="px-6 py-4">
                            <span
                              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                item.isActive
                                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                  : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                              }`}
                            >
                              {item.isActive
                                ? t("news-system.management.table.active")
                                : t("news-system.management.table.inactive")}
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            {getSourceBadge(item.isSystemNews)}
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center gap-2">
                              <Button
                                onClick={() => openViewModal(item)}
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/20"
                              >
                                <Eye size={16} />
                              </Button>
                              {item.isSystemNews ? (
                                <Tooltip
                                  content={t(
                                    "news-system.management.actions.cannotEdit"
                                  )}
                                  placement="top"
                                >
                                  <Button
                                    disabled
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8 text-gray-400 cursor-not-allowed"
                                  >
                                    <PencilSimple size={16} />
                                  </Button>
                                </Tooltip>
                              ) : (
                                <Button
                                  onClick={() => openEditModal(item)}
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50 dark:text-yellow-400 dark:hover:text-yellow-300 dark:hover:bg-yellow-900/20"
                                >
                                  <PencilSimple size={16} />
                                </Button>
                              )}
                              {item.isSystemNews ? (
                                <Tooltip
                                  content={t(
                                    "news-system.management.actions.cannotDelete"
                                  )}
                                  placement="top"
                                >
                                  <Button
                                    disabled
                                    variant="ghost"
                                    size="icon"
                                    className="h-8 w-8 text-gray-400 cursor-not-allowed"
                                  >
                                    <Trash size={16} />
                                  </Button>
                                </Tooltip>
                              ) : (
                                <Button
                                  onClick={() => handleDelete(item)}
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 text-red-600 hover:text-red-800 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
                                >
                                  <Trash size={16} />
                                </Button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>

                  {allNews.length === 0 && (
                    <div className="text-center py-8 text-foreground text-opacity-60">
                      {t("news-system.management.messages.noNews")}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Create/Edit Modal */}
      {showModal && (
        <Modal
          isOpen={showModal}
          onClose={() => {
            setShowModal(false);
            setEditingNews(null);
            resetForm();
          }}
          title={
            editingNews
              ? t("news-system.management.edit")
              : t("news-system.management.create")
          }
        >
          <form onSubmit={handleSubmit} className="space-y-4">
            <FormItem>
              <Label htmlFor="title">
                {t("news-system.management.form.title")}
              </Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) =>
                  setFormData({ ...formData, title: e.target.value })
                }
                placeholder={t("news-system.management.form.titlePlaceholder")}
                required
              />
            </FormItem>

            <FormItem>
              <Label htmlFor="content">
                {t("news-system.management.form.content")}
              </Label>
              <Textarea
                id="content"
                value={formData.content}
                onChange={(e) =>
                  setFormData({ ...formData, content: e.target.value })
                }
                placeholder={t(
                  "news-system.management.form.contentPlaceholder"
                )}
                rows={4}
                required
              />
            </FormItem>

            <FormItem>
              <Label htmlFor="priority">
                {t("news-system.management.form.priority")}
              </Label>
              <select
                id="priority"
                value={formData.priority}
                onChange={(e) =>
                  setFormData({ ...formData, priority: e.target.value })
                }
                className="w-full px-3 py-2 bg-background border border-border rounded-lg text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
              >
                <option value="low">
                  {t("news-system.management.form.priorities.low")}
                </option>
                <option value="medium">
                  {t("news-system.management.form.priorities.medium")}
                </option>
                <option value="high">
                  {t("news-system.management.form.priorities.high")}
                </option>
                <option value="urgent">
                  {t("news-system.management.form.priorities.urgent")}
                </option>
              </select>
            </FormItem>

            <FormItem>
              <Label>{t("news-system.management.form.targetRoles")}</Label>
              <div className="space-y-2">
                {["admin", "manager", "default"].map((role) => (
                  <label key={role} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={formData.targetRoles.includes(role)}
                      onChange={(e) => handleRoleChange(role, e.target.checked)}
                      className="rounded border-border text-primary focus:ring-primary"
                    />
                    <span className="text-foreground">
                      {t(`news-system.management.form.roles.${role}`)}
                    </span>
                  </label>
                ))}
              </div>
            </FormItem>

            <FormItem>
              <Label htmlFor="expiresAt">
                {t("news-system.management.form.expiresAt")}
              </Label>
              <Input
                id="expiresAt"
                type="datetime-local"
                value={formData.expiresAt}
                onChange={(e) =>
                  setFormData({ ...formData, expiresAt: e.target.value })
                }
              />
            </FormItem>

            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                onClick={() => {
                  setShowModal(false);
                  setEditingNews(null);
                  resetForm();
                }}
                variant="ghost"
              >
                {t("news-system.management.actions.cancel")}
              </Button>
              <Button type="submit" variant="default">
                {editingNews
                  ? t("news-system.management.actions.update")
                  : t("news-system.management.actions.create")}
              </Button>
            </div>
          </form>
        </Modal>
      )}

      {/* View Modal */}
      {viewingNews && (
        <Modal
          isOpen={!!viewingNews}
          onClose={() => setViewingNews(null)}
          title={viewingNews.title}
        >
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-foreground/80 mb-2">
                {t("news-system.management.form.content")}
              </h4>
              <div className="prose dark:prose-invert max-w-none bg-secondary/20 p-3 rounded-lg border">
                <Markdown content={viewingNews.content} />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-foreground/80 mb-1">
                  {t("news-system.management.table.priority")}
                </h4>
                <span
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(
                    viewingNews.priority
                  )}`}
                >
                  {t(
                    `news-system.management.form.priorities.${viewingNews.priority}`
                  )}
                </span>
              </div>

              <div>
                <h4 className="text-sm font-medium text-foreground/80 mb-1">
                  {t("news-system.management.table.source")}
                </h4>
                {getSourceBadge(viewingNews.isSystemNews)}
              </div>

              <div>
                <h4 className="text-sm font-medium text-foreground/80 mb-1">
                  {t("news-system.management.table.targetRoles")}
                </h4>
                <p className="text-foreground">
                  {formatTargetRoles(viewingNews.targetRoles)}
                </p>
              </div>

              <div>
                <h4 className="text-sm font-medium text-foreground/80 mb-1">
                  {t("news-system.management.table.created")}
                </h4>
                <p className="text-foreground">
                  {formatDate(viewingNews.createdAt)}
                </p>
              </div>

              <div>
                <h4 className="text-sm font-medium text-foreground/80 mb-1">
                  {t("news-system.management.table.expires")}
                </h4>
                <p className="text-foreground">
                  {formatDate(viewingNews.expiresAt)}
                </p>
              </div>

              <div>
                <h4 className="text-sm font-medium text-foreground/80 mb-1">
                  {t("news-system.management.table.status")}
                </h4>
                <span
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    viewingNews.isActive
                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                      : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                  }`}
                >
                  {viewingNews.isActive
                    ? t("news-system.management.table.active")
                    : t("news-system.management.table.inactive")}
                </span>
              </div>
            </div>

            {viewingNews.isSystemNews && viewingNews.metadata && (
              <div>
                <h4 className="text-sm font-medium text-foreground/80 mb-2">
                  System Information
                </h4>
                <div className="bg-secondary/20 p-3 rounded-lg border text-sm text-foreground/80">
                  {viewingNews.metadata.author && (
                    <p>
                      <strong className="text-foreground">
                        {t("news-system-items.metadata.author")}:
                      </strong>{" "}
                      {translateAuthor(viewingNews.metadata.author)}
                    </p>
                  )}
                  {viewingNews.version && (
                    <p>
                      <strong className="text-foreground">
                        {t("news-system-items.metadata.version")}:
                      </strong>{" "}
                      {viewingNews.version}
                    </p>
                  )}
                  {viewingNews.metadata.category && (
                    <p>
                      <strong className="text-foreground">
                        {t("news-system-items.metadata.category")}:
                      </strong>{" "}
                      {translateCategory(viewingNews.metadata.category)}
                    </p>
                  )}
                  {viewingNews.metadata.tags && (
                    <p>
                      <strong className="text-foreground">Tags:</strong>{" "}
                      {viewingNews.metadata.tags.join(", ")}
                    </p>
                  )}
                </div>
              </div>
            )}

            <div className="flex justify-end pt-4">
              <Button onClick={() => setViewingNews(null)} variant="secondary">
                {t("news-system.close")}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
}
