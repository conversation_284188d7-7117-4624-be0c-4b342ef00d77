import { THREAD_RENAME_EVENT } from "@/components/Sidebar/ActiveWorkspaces/ThreadContainer";
import { emitAssistantMessageCompleteEvent } from "@/components/contexts/TTSProvider";
import i18next from "i18next";
import showToast from "@/utils/toast";
import { tokenManager } from "../tokenizer";

// Function to handle chat response updates
export function handleChatResponse(
  chatResult,
  setLoadingResponse,
  setChatHistory,
  remHistory,
  setWebsocket
) {
  const {
    uuid,
    textResponse,
    type,
    sources = [],
    error,
    close,
    chatId = null,
    action = null,
    metrics = {},
  } = chatResult;

  // Translate the textResponse if it's a translation key
  const translatedResponse = textResponse?.startsWith("show-toast.")
    ? i18next.t(textResponse)
    : textResponse;

  // Handle rename_thread action immediately, regardless of message type
  if (action === "rename_thread") {
    if (!!chatResult?.thread?.slug && chatResult.thread.name) {
      window.dispatchEvent(
        new CustomEvent(THREAD_RENAME_EVENT, {
          detail: {
            threadSlug: chatResult.thread.slug,
            newName: chatResult.thread.name,
          },
        })
      );
    }
  }

  if (type === "abort") {
    setLoadingResponse(false);
    setChatHistory((prevChatHistory) => {
      const lastPendingIndex = prevChatHistory.findLastIndex(
        (msg) => msg.role === "assistant" && msg.pending === true
      );

      if (lastPendingIndex === -1) {
        console.warn(
          `Abort received (uuid: ${uuid}), but no pending assistant message found in history.`
        );
        return prevChatHistory;
      }

      const targetMsgUuid = prevChatHistory[lastPendingIndex]?.uuid;

      return prevChatHistory.map((msg, index) => {
        if (index === lastPendingIndex) {
          return {
            ...msg,
            error: error || "Request aborted.",
            pending: false,
            animate: false,
            closed: true,
            sources: msg.sources || [],
            metrics: msg.metrics || {},
          };
        }
        return msg;
      });
    });
    // Ensure any open Document Drafting Progress modal closes on abort/error
    setTimeout(() => {
      window.dispatchEvent(new Event("DD_PROGRESS_DONE"));
    }, 0);
  } else if (type === "statusResponse") {
    setLoadingResponse(false);
    setChatHistory((prevChatHistory) => [
      ...prevChatHistory,
      {
        type,
        uuid,
        content: translatedResponse,
        role: "assistant",
        sources,
        closed: true,
        error,
        animate: false,
        pending: false,
        metrics,
      },
    ]);
  } else if (type === "textResponse") {
    setLoadingResponse(false);
    setChatHistory((prevChatHistory) => {
      const chatIdx = prevChatHistory.findIndex((chat) => chat.uuid === uuid);

      if (chatIdx !== -1) {
        const existingHistory = { ...prevChatHistory[chatIdx] };
        const updatedHistory = {
          ...existingHistory,
          content: translatedResponse,
          sources: sources || existingHistory.sources,
          closed: close,
          error: error || existingHistory.error,
          animate: !close,
          pending: false,
          chatId: chatId || existingHistory.chatId,
          metrics: metrics || existingHistory.metrics,
        };
        return prevChatHistory.map((msg, index) =>
          index === chatIdx ? updatedHistory : msg
        );
      } else {
        return [
          ...prevChatHistory,
          {
            uuid,
            content: translatedResponse,
            role: "assistant",
            sources,
            closed: close,
            error,
            animate: !close,
            pending: false,
            chatId,
            metrics,
          },
        ];
      }
    });
    // Get the user's question from chat history for automatic estimation
    const userQuestion =
      remHistory && remHistory.length > 0
        ? remHistory[remHistory.length - 1]?.content || ""
        : "";

    emitAssistantMessageCompleteEvent({
      chatId,
      question: userQuestion,
      answer: translatedResponse,
    });
  } else if (
    type === "textResponseChunk" ||
    type === "finalizeResponseStream"
  ) {
    setChatHistory((prevChatHistory) => {
      let chatIdx = prevChatHistory.findIndex((chat) => chat.uuid === uuid);
      let pendingIdx = -1;

      if (chatIdx === -1) {
        pendingIdx = prevChatHistory.findLastIndex(
          (chat) => chat.role === "assistant" && chat.pending === true
        );

        if (pendingIdx !== -1) {
          chatIdx = pendingIdx;
        } else {
          console.warn(
            `Chunk or finalize received for unknown uuid ${uuid} and no pending message found.`
          );
          return prevChatHistory;
        }
      }

      const existingHistory = { ...prevChatHistory[chatIdx] };
      let updatedHistory;

      if (type === "finalizeResponseStream") {
        // When finalizing, ensure we're not creating a duplicate message
        // by keeping the existing content if no new content is provided

        updatedHistory = {
          ...existingHistory,
          uuid: uuid,
          content: translatedResponse || existingHistory.content,
          sources: sources?.length > 0 ? sources : existingHistory.sources,
          closed: true,
          animate: false,
          pending: false,
          chatId: chatId || existingHistory.chatId,
          metrics: metrics || existingHistory.metrics,
        };
        // Get the user's question from chat history for automatic estimation
        const userQuestion =
          remHistory && remHistory.length > 0
            ? remHistory[remHistory.length - 1]?.content || ""
            : "";

        emitAssistantMessageCompleteEvent({
          chatId,
          question: userQuestion,
          answer: translatedResponse || existingHistory.content,
        });
        // Defer to next tick to avoid state update in current render phase
        setTimeout(() => {
          window.dispatchEvent(new Event("DD_PROGRESS_DONE"));
        }, 0);
      } else {
        const isFirstChunk = pendingIdx !== -1;
        const currentContent = isFirstChunk
          ? ""
          : existingHistory.content || "";

        const newContent = currentContent + (translatedResponse || "");

        updatedHistory = {
          ...existingHistory,
          uuid: uuid,
          content: newContent,
          sources: sources?.length > 0 ? sources : existingHistory.sources,
          error,
          closed: close,
          animate: !close,
          pending: isFirstChunk ? false : existingHistory.pending,
          chatId: chatId || existingHistory.chatId,
          metrics: metrics || existingHistory.metrics,
        };
      }
      return prevChatHistory.map((msg, index) =>
        index === chatIdx ? updatedHistory : msg
      );
    });

    if (type === "finalizeResponseStream" || close) {
      setLoadingResponse(false);
    }
  } else if (type === "agentInitWebsocketConnection") {
    setWebsocket(chatResult.websocketUUID);
  } else if (type === "stopGeneration") {
    setLoadingResponse(false);
    setChatHistory((prevChatHistory) => {
      const chatIdx = prevChatHistory.findLastIndex(
        (chat) => chat.role === "assistant" && !chat.closed
      );

      if (chatIdx >= 0) {
        const existingHistory = { ...prevChatHistory[chatIdx] };
        const updatedHistory = {
          ...existingHistory,
          sources: sources?.length > 0 ? sources : existingHistory.sources,
          closed: true,
          error: error || null,
          animate: false,
          pending: false,
          metrics: metrics || existingHistory.metrics,
        };
        return prevChatHistory.map((msg, index) =>
          index === chatIdx ? updatedHistory : msg
        );
      }
      return prevChatHistory;
    });
    // Ensure modal closes on stopGeneration errors
    setTimeout(() => {
      window.dispatchEvent(new Event("DD_PROGRESS_DONE"));
    }, 0);
  } else if (type === "socketId") {
    setWebsocket(chatResult.socketId);
  } else if (type === "thread_deleted") {
    // Handle thread deleted case - potentially remove thread from UI state
  } else if (type === "ddProgress") {
    // Defer to next tick to avoid state update in current render phase
    setTimeout(() => {
      window.dispatchEvent(
        new CustomEvent("DD_PROGRESS_EVENT", { detail: chatResult })
      );
    }, 0);
  }

  if (action === "reset_chat") {
    if (textResponse?.startsWith("show-toast.")) {
      showToast(i18next.t(textResponse), "success");
    }
    setChatHistory([]);
  }
}

export function chatPrompt(workspace) {
  return (
    workspace?.openAiPrompt ??
    "Given the following conversation, relevant context, and a follow up question, reply with an answer to the current question the user is asking. Return only your response to the question given the above information following the users instructions as needed."
  );
}

export function chatQueryRefusalResponse(workspace) {
  return (
    workspace?.queryRefusalResponse ??
    "There is no relevant information in this workspace to answer your query."
  );
}

export function truncatePrompt(prompt, maxTokens = 8000) {
  return tokenManager.truncateToTokens(prompt, maxTokens);
}
