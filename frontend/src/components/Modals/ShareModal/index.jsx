import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import System from "../../../models/system"; // To fetch users
import Workspace from "../../../models/workspace"; // To update shares
import { useTranslation } from "react-i18next";
import useUser from "../../../hooks/useUser";
import { Button } from "../../Button";
import Label from "../../ui/Label"; // Use default import
import Modal from "../../ui/Modal"; // Use standard Modal
import showToast from "../../../utils/toast"; // Use standard toast utility
import { CircleNotch, Check, X } from "@phosphor-icons/react"; // Use existing icon library
import Switch from "../../ui/Switch";

// Define Zod schema (can be empty if validation isn't strictly needed for the form itself)
const formSchema = z.object({}); // Add fields if direct form validation is needed

export default function ShareModal({
  isOpen,
  onClose,
  type,
  workspace,
  thread,
}) {
  const { t } = useTranslation();
  const { user } = useUser();
  const [organizationUsers, setOrganizationUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(true);
  const [errorUsers, setErrorUsers] = useState(null);
  const [sharedWith, setSharedWith] = useState({ users: [], org: false });
  const [loadingShareStatus, setLoadingShareStatus] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submittingUserId, setSubmittingUserId] = useState(null); // Track which user action is submitting
  const [submittingOrg, setSubmittingOrg] = useState(false); // Track org action submission

  const target = type === "workspace" ? workspace : thread;
  const targetName = target?.name;
  const targetId = target?.id;
  const targetSlug = target?.slug; // Needed for workspace API calls

  useForm({
    // reset is unused
    resolver: zodResolver(formSchema),
    defaultValues: {},
  });

  // Fetch organization users
  useEffect(() => {
    async function fetchUsers() {
      if (!isOpen || !user?.organizationId) {
        setOrganizationUsers([]);
        setLoadingUsers(false);
        return;
      }
      setLoadingUsers(true);
      setErrorUsers(null);
      try {
        const { users: fetchedUsers } = await System.getOrganizationUsers();
        // Filter out the current user from the list
        setOrganizationUsers(
          fetchedUsers.filter((u) => u.id !== user.id) || []
        );
      } catch (error) {
        console.error("Failed to fetch organization users:", error);
        setErrorUsers(t("shareModal.errorLoadingUsers"));
        showToast(t("shareModal.errorLoadingUsers"), "error");
      }
      setLoadingUsers(false);
    }
    fetchUsers();
  }, [isOpen, user?.id, user?.organizationId, t]);

  // Fetch current sharing status
  useEffect(() => {
    async function fetchShareStatus() {
      if (!isOpen || !targetId) {
        setLoadingShareStatus(false);
        setSharedWith({ users: [], org: false });
        return;
      }
      setLoadingShareStatus(true);
      try {
        const { success, sharedUserIds, sharedWithOrg, error } =
          type === "workspace"
            ? await Workspace.getShareStatus(targetSlug)
            : await Workspace.getThreadShareStatus(targetId);

        if (success) {
          setSharedWith({
            users: sharedUserIds || [],
            org: sharedWithOrg || false,
          });
        } else {
          throw new Error(error || "Failed to fetch share status");
        }
      } catch (error) {
        console.error("Failed to fetch share status:", error);
        showToast(t("shareModal.errorLoadingStatus"), "error");
        setSharedWith({ users: [], org: target?.sharedWithOrg || false }); // Fallback potentially
      } finally {
        setLoadingShareStatus(false);
      }
    }
    fetchShareStatus();
  }, [isOpen, targetId, type, targetSlug, t]); // Removed target.sharedWithOrg dependency

  const handleShareUpdate = async (userId, shouldShare) => {
    if (!targetId) return;
    setSubmittingUserId(userId);
    setIsSubmitting(true);
    let success = false;
    let errorMsg = null;

    try {
      let result;
      if (type === "workspace") {
        result = shouldShare
          ? await Workspace.share(targetSlug, { userIds: [userId] })
          : await Workspace.revokeShareUser(targetSlug, userId);
      } else {
        // type === 'thread'
        result = shouldShare
          ? await Workspace.shareThread(targetId, { userIds: [userId] })
          : await Workspace.revokeThreadShareUser(targetId, userId);
      }

      if (result.success) {
        success = true;
        setSharedWith((prev) => ({
          ...prev,
          users: shouldShare
            ? [...prev.users, userId]
            : prev.users.filter((id) => id !== userId),
        }));
        showToast(
          shouldShare
            ? t("shareModal.userAccessGranted")
            : t("shareModal.userAccessRevoked"),
          "success"
        );
      } else {
        throw new Error(result.error || "Failed to update user share");
      }
    } catch (error) {
      console.error("Failed to update share:", error);
      showToast(t("shareModal.errorUpdateUser"), "error");
      errorMsg = error.message;
    } finally {
      setIsSubmitting(false);
      setSubmittingUserId(null);
    }
    return { success, error: errorMsg };
  };

  const handleOrgShareUpdate = async (shouldShare) => {
    if (!targetId) return;
    setSubmittingOrg(true);
    setIsSubmitting(true);
    let success = false;
    let errorMsg = null;

    try {
      let result;
      if (type === "workspace") {
        result = await Workspace.share(targetSlug, {
          shareWithOrg: shouldShare,
        });
      } else {
        // type === 'thread'
        result = await Workspace.shareThread(targetId, {
          shareWithOrg: shouldShare,
        });
      }

      if (result.success) {
        success = true;
        setSharedWith((prev) => ({ ...prev, org: shouldShare }));
        showToast(
          shouldShare
            ? t("shareModal.orgAccessGranted")
            : t("shareModal.orgAccessRevoked"),
          "success"
        );
      } else {
        throw new Error(result.error || "Failed to update org share");
      }
    } catch (error) {
      console.error("Failed to update org share:", error);
      showToast(t("shareModal.errorUpdateOrg"), "error");
      errorMsg = error.message;
    } finally {
      setIsSubmitting(false);
      setSubmittingOrg(false);
    }
    return { success, error: errorMsg };
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <div className="flex flex-col gap-y-1">
        <h2 className="text-lg font-semibold">
          {type === "workspace"
            ? t("shareModal.titleWorkspace")
            : t("shareModal.titleThread")}
        </h2>
        <p className="text-sm text-gray-500 dark:text-stone-400">
          {targetName}
        </p>
      </div>

      <div className="my-4">
        {/* Organization-wide sharing */}
        {user?.organizationId && (
          <div className="flex items-center justify-between p-3 rounded bg-gray-100 dark:bg-stone-800 mb-4">
            <Label
              htmlFor="org-share-switch"
              className="flex flex-col flex-grow mr-4"
            >
              <span>{t("shareModal.shareWithOrg")}</span>
            </Label>
            {submittingOrg ? (
              <CircleNotch className="h-5 w-5 animate-spin text-gray-500" />
            ) : (
              <Switch
                id="org-share-switch"
                checked={sharedWith.org}
                onCheckedChange={handleOrgShareUpdate}
                disabled={isSubmitting}
              />
            )}
          </div>
        )}

        {/* User list */}
        <h3 className="text-md font-medium mb-2">
          {t("shareModal.shareWithUsers")}
        </h3>
        {loadingUsers || loadingShareStatus ? (
          <div className="flex justify-center items-center h-20">
            <CircleNotch className="h-6 w-6 animate-spin text-gray-500" />
          </div>
        ) : errorUsers ? (
          <p className="text-red-500 text-center">{errorUsers}</p>
        ) : organizationUsers.length === 0 ? (
          <p className="text-gray-500 dark:text-stone-400 text-center">
            {t("shareModal.noUsersFound")}
          </p>
        ) : (
          <ul className="max-h-60 overflow-y-auto space-y-2">
            {organizationUsers.map((orgUser) => {
              const isShared = sharedWith.users.includes(orgUser.id);
              return (
                <li
                  key={orgUser.id}
                  className="flex items-center justify-between p-3 rounded hover:bg-gray-50 dark:hover:bg-stone-700/80"
                >
                  <span className="text-sm font-medium">
                    {orgUser.username || t("common.unknownUser")}
                  </span>
                  {submittingUserId === orgUser.id ? (
                    <CircleNotch className="h-5 w-5 animate-spin text-gray-500" />
                  ) : (
                    <Button
                      variant={isShared ? "outline" : "default"}
                      size="sm"
                      onClick={() => handleShareUpdate(orgUser.id, !isShared)}
                      disabled={isSubmitting}
                      className="flex items-center gap-1"
                    >
                      {isShared ? (
                        <>
                          <X size={16} />
                          {t("shareModal.revokeAccess")}
                        </>
                      ) : (
                        <>
                          <Check size={16} />
                          {t("shareModal.grantAccess")}
                        </>
                      )}
                    </Button>
                  )}
                </li>
              );
            })}
          </ul>
        )}
      </div>

      <div className="flex justify-end mt-4">
        <Button variant="outline" onClick={onClose}>
          {t("shareModal.close")}
        </Button>
      </div>
    </Modal>
  );
}
