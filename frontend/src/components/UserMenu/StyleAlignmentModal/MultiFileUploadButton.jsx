import React, { useRef, useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/Button";
import Modal from "@/components/ui/Modal";
import { UploadSimple, Trash } from "@phosphor-icons/react";
import showToast from "@/utils/toast";
import System from "@/models/system";
import { baseHeaders } from "@/utils/request";
import { API_BASE } from "@/utils/constants";
import { TokenManager } from "@/utils/tokenizer";

/**
 * A component for uploading multiple files for style reference with badge and file management
 */
export default function MultiFileUploadButton({
  onFilesChange,
  variant = "outline",
  size = "sm",
  className = "",
  buttonText,
  acceptedFileTypes = ".docx",
  resetTrigger, // External trigger to reset files
  tokenLimits = null, // Token limits from parent component
}) {
  const { t } = useTranslation();
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [showFileModal, setShowFileModal] = useState(false);
  const fileInputRef = useRef(null);
  const uploadedFilesRef = useRef([]); // Track current files to avoid closure issues
  const lastResetTriggerRef = useRef(0); // Track the last reset trigger value

  // Token counting state
  const [currentTokenCount, setCurrentTokenCount] = useState(0);
  const [tokenManager] = useState(() => new TokenManager("gpt-3.5-turbo")); // Default model

  // Calculate total tokens from all uploaded files
  const calculateTotalTokens = (files) => {
    let totalTokens = 0;
    for (const file of files) {
      if (file.content) {
        totalTokens += tokenManager.countFromString(file.content);
      }
    }
    return totalTokens;
  };

  // Update ref whenever uploadedFiles changes
  useEffect(() => {
    uploadedFilesRef.current = uploadedFiles;

    // Calculate and update token count
    const tokenCount = calculateTotalTokens(uploadedFiles);
    setCurrentTokenCount(tokenCount);

    // Pass token information back to parent
    onFilesChange?.(uploadedFiles, tokenCount);
  }, [uploadedFiles, tokenManager]);

  // Reset files when external trigger changes
  useEffect(() => {
    if (resetTrigger > 0 && resetTrigger !== lastResetTriggerRef.current) {
      lastResetTriggerRef.current = resetTrigger;

      // Cleanup all docx sessions before resetting
      const cleanupAndReset = async () => {
        const currentFiles = uploadedFilesRef.current;
        for (const file of currentFiles) {
          if (file.sessionId) {
            await cleanupDocxSession(file.sessionId);
          }
        }
        setUploadedFiles([]);
        setCurrentTokenCount(0);
        onFilesChange?.([], 0);
      };

      cleanupAndReset();
    }
  }, [resetTrigger]); // Only depend on resetTrigger

  // Handle button click - show file modal if files exist, otherwise trigger file input
  const handleButtonClick = () => {
    if (uploadedFiles.length > 0) {
      setShowFileModal(true);
    } else {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    }
  };

  // Handle file selection
  const handleFileChange = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    // Check if files are supported type (currently only DOCX)
    const unsupportedFiles = files.filter(
      (file) => !file.name.toLowerCase().endsWith(".docx")
    );

    if (unsupportedFiles.length > 0) {
      showToast(
        t("docx-edit.file-type-note", "Only .docx files are supported"),
        "error"
      );
      return;
    }

    try {
      setIsProcessing(true);
      const newFiles = [];

      for (const file of files) {
        // Check for duplicates
        const isDuplicate = uploadedFiles.some(
          (existingFile) =>
            existingFile.name === file.name && existingFile.size === file.size
        );

        if (isDuplicate) {
          showToast(t("style-upload.file-already-exists"), "error");
          continue;
        }

        try {
          const result = await System.uploadDocxForTemplate(file);

          if (result.success) {
            const newFile = {
              id: Date.now() + Math.random(),
              name: file.name,
              size: file.size,
              uploadedAt: new Date(),
              content: result.data.content,
              sessionId: result.data.sessionId, // Store sessionId for cleanup
            };
            newFiles.push(newFile);
          } else {
            showToast(
              `Failed to upload ${file.name}: ${result.error || "Unknown error"}`,
              "error"
            );
          }
        } catch (error) {
          console.error("Error uploading file:", error);
          showToast(`Error uploading ${file.name}`, "error");
        }
      }

      if (newFiles.length > 0) {
        const updatedFiles = [...uploadedFiles, ...newFiles];
        setUploadedFiles(updatedFiles);
        // Token count will be recalculated in useEffect
        showToast(t("style-upload.files-uploaded"), "success");
      }
    } finally {
      setIsProcessing(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  // Remove a file
  const handleRemoveFile = async (fileId) => {
    const fileToRemove = uploadedFiles.find((file) => file.id === fileId);

    // Cleanup the docx session if sessionId exists
    if (fileToRemove?.sessionId) {
      await cleanupDocxSession(fileToRemove.sessionId);
    }

    const updatedFiles = uploadedFiles.filter((file) => file.id !== fileId);
    setUploadedFiles(updatedFiles);
    // Token count will be recalculated in useEffect
  };

  // Add more files from the modal
  const handleAddMoreFiles = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Reset all files
  const resetFiles = () => {
    setUploadedFiles([]);
    setCurrentTokenCount(0);
    onFilesChange?.([], 0);
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Format upload date
  const formatUploadDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString([], {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Add cleanup function for docx-edit sessions
  const cleanupDocxSession = async (sessionId) => {
    if (!sessionId) return;

    try {
      const response = await fetch(
        `${API_BASE}/docx-edit/cleanup/${sessionId}`,
        {
          method: "DELETE",
          headers: baseHeaders(),
        }
      );

      if (!response.ok) {
        console.warn(`Failed to cleanup docx session ${sessionId}`);
      }
    } catch (error) {
      console.error("Error cleaning up docx session:", error);
    }
  };

  // Clear all files
  const handleClearAll = async () => {
    // Cleanup all docx sessions
    for (const file of uploadedFiles) {
      if (file.sessionId) {
        await cleanupDocxSession(file.sessionId);
      }
    }

    setUploadedFiles([]);
    setCurrentTokenCount(0);
    onFilesChange?.([], 0);
    setShowFileModal(false);
  };

  return (
    <>
      <div className="relative">
        <Button
          type="button"
          variant={variant}
          size={size}
          onClick={handleButtonClick}
          disabled={isProcessing}
          isLoading={isProcessing}
          className={className}
        >
          {isProcessing ? (
            <>{buttonText || t("user-menu.style-upload")}</>
          ) : (
            <>
              <UploadSimple />
              {buttonText || t("user-menu.style-upload")}
            </>
          )}
        </Button>

        {/* Badge showing file count */}
        {uploadedFiles.length > 0 && (
          <div className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs rounded-full min-w-[20px] h-5 flex items-center justify-center px-1">
            {uploadedFiles.length}
          </div>
        )}
      </div>

      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept={acceptedFileTypes}
        multiple
        className="hidden"
      />

      {/* File Management Modal */}
      {showFileModal && (
        <Modal
          isOpen={showFileModal}
          onClose={() => setShowFileModal(false)}
          title={t("style-upload.manage-files", "Manage Style Reference Files")}
          className="max-w-2xl"
        >
          <div className="space-y-4">
            {/* File list */}
            <div className="max-h-64 overflow-y-auto space-y-2">
              {uploadedFiles.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center justify-between gap-x-3 border p-3 rounded"
                >
                  <div className="min-w-0 flex-1">
                    <div className="text-sm font-medium text-foreground truncate">
                      {file.name}
                    </div>
                    <div className="text-xs text-foreground/60">
                      {formatFileSize(file.size)} •{" "}
                      {formatUploadDate(file.uploadedAt)}
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => handleRemoveFile(file.id)}
                    className="text-foreground hover:text-red-500 flex-shrink-0 p-1"
                    title={t("style-upload.remove-file", "Remove file")}
                  >
                    <Trash size={16} />
                  </button>
                </div>
              ))}
            </div>

            {/* Token Information Display */}
            {tokenLimits && uploadedFiles.length > 0 && (
              <div className="space-y-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-foreground/70">
                    {t("style-upload.token-usage", "Token Usage")}:
                  </span>
                  <span
                    className={`font-medium ${
                      currentTokenCount > tokenLimits.availableTokens
                        ? "text-red-600"
                        : currentTokenCount > tokenLimits.availableTokens * 0.8
                          ? "text-yellow-600"
                          : "text-green-600"
                    }`}
                  >
                    {currentTokenCount.toLocaleString()} /{" "}
                    {tokenLimits.availableTokens.toLocaleString()}
                  </span>
                </div>

                {/* Progress bar */}
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      currentTokenCount > tokenLimits.availableTokens
                        ? "bg-red-500"
                        : currentTokenCount > tokenLimits.availableTokens * 0.8
                          ? "bg-yellow-500"
                          : "bg-green-500"
                    }`}
                    style={{
                      width: `${Math.min(
                        (currentTokenCount / tokenLimits.availableTokens) * 100,
                        100
                      )}%`,
                    }}
                  />
                </div>

                {/* Warning message if over limit */}
                {currentTokenCount > tokenLimits.availableTokens && (
                  <div className="text-xs text-red-600 mt-1">
                    {t(
                      "style-upload.token-limit-warning",
                      "Your files exceed the token limit. Please remove content or files to continue."
                    )}
                  </div>
                )}

                {/* Context window info */}
                <div className="text-xs text-foreground/50">
                  {t("style-upload.context-window-info", {
                    contextWindow: tokenLimits.contextWindow.toLocaleString(),
                    reservedTokens: tokenLimits.reservedTokens.toLocaleString(),
                    defaultValue: `Context window: ${tokenLimits.contextWindow.toLocaleString()} tokens (${tokenLimits.reservedTokens.toLocaleString()} reserved for processing)`,
                  })}
                </div>
              </div>
            )}

            {/* Action buttons */}
            <div className="flex justify-between items-center pt-4 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={handleAddMoreFiles}
                disabled={isProcessing}
              >
                <UploadSimple size={16} />
                {t("style-upload.add-more", "Add More Files")}
              </Button>

              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClearAll}
                  className="text-red-600 hover:text-red-700"
                >
                  {t("style-upload.clear-all", "Clear All")}
                </Button>
                <Button type="button" onClick={() => setShowFileModal(false)}>
                  {t("common.done", "Done")}
                </Button>
              </div>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
}

// Export the reset function for external use
MultiFileUploadButton.resetFiles = (setUploadedFiles, onFilesChange) => {
  setUploadedFiles([]);
  if (onFilesChange) {
    onFilesChange([]);
  }
};
