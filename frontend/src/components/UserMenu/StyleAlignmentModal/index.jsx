import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Trash, Eye, PencilSimple } from "@phosphor-icons/react";
import { Button } from "@/components/Button";
import Modal from "@/components/ui/Modal";
import MultiFileUploadButton from "./MultiFileUploadButton";
import Toggle from "@/components/ui/Toggle";
import Input from "@/components/ui/Input";
import Textarea from "@/components/ui/Textarea";
import Label from "@/components/ui/Label";
import System from "@/models/system";
import showToast from "@/utils/toast";
import useUser from "@/hooks/useUser";
import { useCallback } from "react";
import { TokenManager } from "@/utils/tokenizer";
import {
  useStyleProfiles,
  useAddStyleProfile,
  useUpdateStyleProfile,
  useDeleteStyleProfile,
  useActiveStyleProfileId,
  useSetActiveStyleProfile,
  useStyleAlignmentEnabled,
  useSetStyleAlignmentEnabled,
} from "@/stores/userStore";

const tokenManager = new TokenManager();

export default function StyleAlignmentModal({ hideModal }) {
  const { t } = useTranslation();
  const { user } = useUser();
  const styleProfiles = useStyleProfiles();
  const addStyleProfile = useAddStyleProfile();
  const updateStyleProfile = useUpdateStyleProfile();
  const deleteStyleProfile = useDeleteStyleProfile();
  const activeProfileId = useActiveStyleProfileId();
  const setActiveStyleProfile = useSetActiveStyleProfile();
  const enabled = useStyleAlignmentEnabled();
  const setEnabled = useSetStyleAlignmentEnabled();

  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [canGenerate, setCanGenerate] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showPromptModal, setShowPromptModal] = useState(false);
  const [promptContent, setPromptContent] = useState("");
  const [isLoadingPrompt, setIsLoadingPrompt] = useState(false);
  const [resetTrigger, setResetTrigger] = useState(0);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingProfile, setEditingProfile] = useState(null);
  const [editName, setEditName] = useState("");
  const [editInstructions, setEditInstructions] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  // Token limit state
  const [tokenLimits, setTokenLimits] = useState({
    contextWindow: 0,
    availableTokens: 0,
    reservedTokens: 0,
  });
  const [currentTokenCount, setCurrentTokenCount] = useState(0);
  const [isLoadingTokenLimits, setIsLoadingTokenLimits] = useState(true);

  const isAdmin = user && user.role === "admin";
  const hasProfiles = styleProfiles.length > 0;

  // Load token limits on component mount
  useEffect(() => {
    const loadTokenLimits = async () => {
      try {
        const response = await System.getStyleGenerationContextWindow();
        if (response.success) {
          setTokenLimits({
            contextWindow: response.contextWindow,
            availableTokens: response.availableTokens,
            reservedTokens: response.reservedTokens,
          });
        } else {
          console.error("Failed to load token limits:", response.error);
        }
      } catch (error) {
        console.error("Error loading token limits:", error);
      } finally {
        setIsLoadingTokenLimits(false);
      }
    };

    loadTokenLimits();
  }, []);

  // Handle files change from upload component
  const handleFilesChange = useCallback((files, tokenCount = 0) => {
    setUploadedFiles(files);
    setCurrentTokenCount(tokenCount);
    setCanGenerate(files.length > 0);
  }, []);

  const handleGenerate = async () => {
    if (uploadedFiles.length === 0) {
      showToast(
        t("style-upload.no-files", "Please upload at least one file"),
        "error"
      );
      return;
    }

    // Check token limits
    if (currentTokenCount > tokenLimits.availableTokens) {
      showToast(
        t("style-upload.token-limit-exceeded", {
          current: currentTokenCount.toLocaleString(),
          available: tokenLimits.availableTokens.toLocaleString(),
          defaultValue: `Token limit exceeded. Your files contain ${currentTokenCount.toLocaleString()} tokens, but only ${tokenLimits.availableTokens.toLocaleString()} tokens are available for processing.`,
        }),
        "error"
      );
      return;
    }

    setIsGenerating(true);
    try {
      // Combine content from all uploaded files
      const combinedContent = uploadedFiles
        .map((file) => file.content)
        .join("\n\n---\n\n");

      const response = await System.generateStyleProfile(combinedContent);

      if (response.success && response.styleInstructions) {
        const now = new Date();
        const id = now.getTime().toString();
        const profileName = `${t("user-menu.style-profile")} ${now.toLocaleDateString()} ${now.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}`;

        addStyleProfile({
          id,
          name: profileName,
          instructions: response.styleInstructions,
          createdAt: now.toISOString(),
          sourceFiles: uploadedFiles.map((f) => ({
            name: f.name,
            size: f.size,
          })), // Store file metadata
        });

        setActiveStyleProfile(id);

        // Reset uploaded files and trigger reset in MultiFileUploadButton
        setUploadedFiles([]);
        setCurrentTokenCount(0);
        setCanGenerate(false);
        setResetTrigger((prev) => prev + 1); // Trigger reset in child component

        setEnabled(true);

        showToast(t("user-menu.style-generated-success"), "success");
      } else {
        showToast(
          response.error || t("user-menu.style-generation-failed"),
          "error"
        );
      }
    } catch (error) {
      console.error("Error generating style profile:", error);
      showToast(t("user-menu.style-generation-failed"), "error");
    } finally {
      setIsGenerating(false);
    }
  };

  const handleViewPrompt = async () => {
    setIsLoadingPrompt(true);
    setShowPromptModal(true);

    try {
      const response = await System.getStyleGenerationPrompt();

      if (response.success && response.prompt) {
        setPromptContent(response.prompt);
      } else {
        showToast(response.error || t("user-menu.prompt-error"), "error");
        setPromptContent(t("user-menu.prompt-error"));
      }
    } catch (error) {
      console.error("Error fetching style generation prompt:", error);
      showToast(t("user-menu.prompt-error"), "error");
      setPromptContent(t("user-menu.prompt-error"));
    } finally {
      setIsLoadingPrompt(false);
    }
  };

  const formatProfileDate = (profile) => {
    if (profile.createdAt) {
      const date = new Date(profile.createdAt);
      return `${date.toLocaleDateString()} ${date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}`;
    }
    // Fallback for existing profiles without createdAt
    return new Date().toLocaleDateString();
  };

  const handleEditProfile = (profile) => {
    setEditingProfile(profile);
    setEditName(profile.name);
    setEditInstructions(profile.instructions);
    setShowEditModal(true);
  };

  const handleSaveEdit = async () => {
    if (!editingProfile || !editName.trim()) {
      showToast(
        t("user-menu.profile-name-required", "Profile name is required"),
        "error"
      );
      return;
    }

    setIsSaving(true);
    try {
      // Update the profile in the store
      updateStyleProfile(editingProfile.id, {
        name: editName.trim(),
        instructions: editInstructions.trim(),
      });

      showToast(t("user-menu.profile-updated-success"), "success");
      setShowEditModal(false);
      setEditingProfile(null);
      setEditName("");
      setEditInstructions("");
    } catch (error) {
      console.error("Error updating profile:", error);
      showToast(t("user-menu.profile-update-failed"), "error");
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancelEdit = () => {
    setShowEditModal(false);
    setEditingProfile(null);
    setEditName("");
    setEditInstructions("");
  };

  return (
    <>
      <Modal
        isOpen={true}
        onClose={hideModal}
        title={t("user-menu.style-alignment")}
        className="max-w-md"
      >
        <div className="space-y-4">
          {/* Description */}
          <p className="text-sm text-foreground/70">
            {t(
              "style-upload.manage-files-description",
              "In here you can upload files you have drafted to generate a personalized styling. When this is active, output in the platform will align more closely with your personal professional writing style."
            )}
          </p>

          {/* Only show toggle if there are profiles */}
          {hasProfiles && (
            <>
              <div className="flex items-center gap-x-2">
                <Toggle
                  id="styleEnabled"
                  name="styleEnabled"
                  checked={enabled}
                  disabled={!activeProfileId}
                  onChange={(e) => setEnabled(e.target.checked)}
                />
                <label
                  htmlFor="styleEnabled"
                  className="text-sm text-foreground"
                >
                  {t("user-menu.style-enabled")}
                </label>
              </div>
              <div className="border-b" />
            </>
          )}

          {/* Style Profiles Section */}
          {hasProfiles && (
            <>
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-foreground">
                  {t("user-menu.style-profiles")}
                </h4>
                <div className="max-h-32 overflow-y-auto space-y-2">
                  {styleProfiles.map((profile) => (
                    <div
                      key={profile.id}
                      className="flex items-center justify-between gap-x-2 border p-2 rounded"
                    >
                      <div className="flex items-center gap-x-2 min-w-0 flex-1">
                        <input
                          type="radio"
                          name="activeStyle"
                          checked={activeProfileId === profile.id}
                          onChange={() => setActiveStyleProfile(profile.id)}
                          className="flex-shrink-0"
                        />
                        <div className="min-w-0 flex-1">
                          <div className="text-sm text-foreground truncate">
                            {profile.name}
                          </div>
                          <div className="text-xs text-foreground/60">
                            {formatProfileDate(profile)}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-x-2">
                        <button
                          type="button"
                          onClick={() => handleEditProfile(profile)}
                          className="text-foreground hover:text-blue-500 flex-shrink-0"
                        >
                          <PencilSimple size={16} />
                        </button>
                        <button
                          type="button"
                          onClick={() => deleteStyleProfile(profile.id)}
                          className="text-foreground hover:text-red-500 flex-shrink-0"
                        >
                          <Trash size={16} />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="border-b" />
            </>
          )}

          {/* Upload and Generate Section */}
          <div className="space-y-6">
            <div className="flex justify-center">
              <MultiFileUploadButton
                onFilesChange={handleFilesChange}
                buttonText={t("user-menu.style-upload")}
                resetTrigger={resetTrigger}
                tokenLimits={tokenLimits}
              />
            </div>

            {/* Token Limit Display */}
            {!isLoadingTokenLimits && (
              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-foreground/70">
                    {t("style-upload.token-usage", "Token Usage")}:
                  </span>
                  <span
                    className={`font-medium ${
                      currentTokenCount > tokenLimits.availableTokens
                        ? "text-red-500"
                        : currentTokenCount > tokenLimits.availableTokens * 0.8
                          ? "text-yellow-500"
                          : "text-green-500"
                    }`}
                  >
                    {currentTokenCount.toLocaleString()} /{" "}
                    {tokenLimits.availableTokens.toLocaleString()}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${
                      currentTokenCount > tokenLimits.availableTokens
                        ? "bg-red-500"
                        : currentTokenCount > tokenLimits.availableTokens * 0.8
                          ? "bg-yellow-500"
                          : "bg-green-500"
                    }`}
                    style={{
                      width: `${Math.min(100, (currentTokenCount / tokenLimits.availableTokens) * 100)}%`,
                    }}
                  />
                </div>
                {currentTokenCount > tokenLimits.availableTokens && (
                  <p className="text-xs text-red-500">
                    {t(
                      "style-upload.token-limit-warning",
                      "Your files exceed the token limit. Please remove some content or files to proceed."
                    )}
                  </p>
                )}
                <p className="text-xs text-foreground/60">
                  {t("style-upload.context-window-info", {
                    contextWindow: tokenLimits.contextWindow.toLocaleString(),
                    reservedTokens: tokenLimits.reservedTokens.toLocaleString(),
                    defaultValue: `Context window: ${tokenLimits.contextWindow.toLocaleString()} tokens (${tokenLimits.reservedTokens.toLocaleString()} reserved for processing)`,
                  })}
                </p>
              </div>
            )}

            <Button
              type="button"
              disabled={!canGenerate || isGenerating}
              isLoading={isGenerating}
              onClick={handleGenerate}
              className="w-full"
            >
              {t("user-menu.style-generate")}
            </Button>
            {isAdmin && (
              <Button
                type="button"
                variant="outline"
                onClick={handleViewPrompt}
                className="w-full flex items-center justify-center gap-x-2"
              >
                <Eye size={16} />
                {t("user-menu.view-prompt")}
              </Button>
            )}
          </div>
        </div>
      </Modal>

      {/* Prompt Modal */}
      {showPromptModal && (
        <Modal
          isOpen={showPromptModal}
          onClose={() => setShowPromptModal(false)}
          title={t("user-menu.prompt-modal-title")}
          className="max-w-4xl max-h-[90vh] flex flex-col"
        >
          <div className="flex-1 min-h-0 overflow-auto">
            {isLoadingPrompt ? (
              <div className="text-center py-8">
                <p className="text-foreground">
                  {t("user-menu.prompt-loading")}
                </p>
              </div>
            ) : (
              <pre className="whitespace-pre-wrap text-sm text-foreground bg-gray-100 dark:bg-gray-800 p-4 rounded border">
                {promptContent}
              </pre>
            )}
          </div>
        </Modal>
      )}

      {/* Edit Modal */}
      {showEditModal && (
        <Modal
          isOpen={showEditModal}
          onClose={handleCancelEdit}
          title={t("user-menu.edit-profile")}
          className="max-w-4xl max-h-[90vh]"
        >
          <div className="space-y-4">
            <div>
              <Label htmlFor="editName">
                {t("user-menu.edit-profile-name")}
              </Label>
              <Input
                id="editName"
                name="editName"
                value={editName}
                onChange={(e) => setEditName(e.target.value)}
                required
                className="mt-1"
              />
            </div>
            <div className="flex-1">
              <Label htmlFor="editInstructions">
                {t("user-menu.edit-profile-instructions")}
              </Label>
              <Textarea
                id="editInstructions"
                name="editInstructions"
                value={editInstructions}
                onChange={(e) => setEditInstructions(e.target.value)}
                rows={20}
                maxLength={50000}
                required
                className="mt-1 min-h-[400px]"
              />
              <div className="text-xs text-foreground/60 mt-1 text-right">
                {editInstructions.length}/50,000 characters
              </div>
            </div>
            <div className="flex gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancelEdit}
                disabled={isSaving}
                className="flex-1"
              >
                {t("user-menu.cancel-edit")}
              </Button>
              <Button
                type="button"
                onClick={handleSaveEdit}
                disabled={isSaving}
                className="flex-1"
              >
                {isSaving
                  ? t("common.saving", "Saving...")
                  : t("user-menu.save-changes")}
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
}
