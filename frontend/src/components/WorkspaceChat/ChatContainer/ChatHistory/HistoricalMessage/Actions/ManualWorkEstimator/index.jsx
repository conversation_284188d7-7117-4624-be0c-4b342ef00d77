import { useState } from "react";
import { useTranslation } from "react-i18next";
import { BsStopwatch, BsChevronDown, BsChevronUp } from "react-icons/bs";
import { CircleNotch } from "@phosphor-icons/react";
import Modal from "@/components/ui/Modal";
import Markdown from "@/components/ui/Markdown";
import { Button } from "@/components/Button";
import System from "@/models/system";
import showToast from "@/utils/toast";
import {
  extractTotalHours,
  storeEstimationResult,
} from "@/utils/manualWorkEstimation";

export default function ManualWorkEstimator({
  question,
  answer,
  user,
  threadSlug,
  chatId,
}) {
  const { t } = useTranslation();
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [result, setResult] = useState(null);
  const [showPrompt, setShowPrompt] = useState(false);

  // Check if user can view prompt details (admin only)
  const canViewPrompt = user?.role === "admin";

  const handleEstimate = async () => {
    setIsLoading(true);
    try {
      const response = await System.estimateManualWork({ question, answer });
      if (response.success) {
        setResult(response.result);

        // Extract total hours and store the estimation result
        if (response.result?.textResponse && threadSlug && chatId) {
          const totalHours = extractTotalHours(response.result.textResponse);
          if (totalHours && totalHours > 0) {
            storeEstimationResult(
              threadSlug,
              chatId,
              response.result.textResponse,
              totalHours
            );
          }
        }
      } else {
        showToast(
          t("show-toast.manual-work-estimate-error") +
            (response.error || "Unknown error"),
          "error"
        );
      }
    } catch (error) {
      showToast(
        t("show-toast.manual-work-estimate-error") + error.message,
        "error"
      );
    } finally {
      setIsLoading(false);
      setIsOpen(true);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    setShowPrompt(false);
  };

  const togglePrompt = () => setShowPrompt(!showPrompt);

  const renderPromptContent = () => {
    if (!result?.prompt) return null;

    return (
      <div className="mt-4 space-y-4 bg-muted p-4 rounded-lg">
        <h4 className="font-semibold text-foreground">
          {t("manual-work-estimator.prompt-title")}
        </h4>

        <div>
          <h5 className="font-medium text-foreground mb-2">
            {t("manual-work-estimator.system-prompt")}
          </h5>
          <div className="bg-background p-3 rounded border text-sm text-foreground">
            <Markdown content={result.prompt.systemPrompt} />
          </div>
        </div>

        <div>
          <h5 className="font-medium text-foreground mb-2">
            {t("manual-work-estimator.user-content")}
          </h5>
          <div className="bg-background p-3 rounded border text-sm text-foreground max-h-40 overflow-y-auto">
            <pre className="whitespace-pre-wrap font-mono text-xs">
              {result.prompt.userContent}
            </pre>
          </div>
        </div>

        <div>
          <h5 className="font-medium text-foreground mb-2">
            {t("manual-work-estimator.provider-info")}
          </h5>
          <div className="bg-background p-3 rounded border text-sm text-foreground">
            <p>
              <strong>{t("manual-work-estimator.provider")}:</strong>{" "}
              {result.prompt.provider}
            </p>
            <p>
              <strong>{t("manual-work-estimator.model")}:</strong>{" "}
              {result.prompt.model}
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={handleEstimate}
        disabled={isLoading}
      >
        {isLoading ? <CircleNotch className="animate-spin" /> : <BsStopwatch />}
        {t("manual-work-estimator.button")}
      </Button>
      <Modal
        isOpen={isOpen}
        onClose={handleClose}
        title={t("manual-work-estimator.title")}
      >
        <div className="w-full px-2 overflow-y-auto max-h-[40rem]">
          {result && (
            <>
              <Markdown
                content={
                  typeof result.textResponse === "string"
                    ? result.textResponse
                    : typeof result === "string"
                      ? result
                      : "No estimation result available"
                }
              />

              {result.prompt && canViewPrompt && (
                <div className="mt-4 border-t pt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={togglePrompt}
                    className="flex items-center gap-2"
                  >
                    {showPrompt ? (
                      <BsChevronUp size={16} />
                    ) : (
                      <BsChevronDown size={16} />
                    )}
                    {showPrompt
                      ? t("manual-work-estimator.hide-prompt")
                      : t("manual-work-estimator.show-prompt")}
                  </Button>

                  {showPrompt && renderPromptContent()}
                </div>
              )}
            </>
          )}
        </div>
      </Modal>
    </>
  );
}
