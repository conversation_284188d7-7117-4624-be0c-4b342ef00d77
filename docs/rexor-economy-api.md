# Rexor Economy API Integration

## Overview

The Rexor economy API integration provides seamless connection between ISTLegal and the Rexor economy system for automated invoice logging and project management. This integration includes automatic manual work time estimation and transaction management.

## Architecture

### Core Components

1. **Frontend Service Layer** (`frontend/src/services/rexorService.js`)
   - Authentication with Rexor API
   - Project registration
   - Transaction management
   - Manual work estimation integration

2. **React Hook** (`frontend/src/hooks/useRexor.js`)
   - State management for Rexor authentication
   - Transaction operations
   - Error handling and session management

3. **Automatic Integration Hook** (`frontend/src/hooks/useAutoRexorIntegration.js`)
   - Background processing of AI responses
   - Automatic manual work estimation
   - Seamless Rexor transaction creation

4. **Backend Endpoints** (`server/endpoints/system.js`)
   - Proxy endpoints for Rexor API calls
   - Authentication handling
   - Transaction processing

## Features

### 1. Authentication

#### Login Process
- Users authenticate with Rex<PERSON> using username/password
- Credentials are securely stored with encryption
- Access tokens are managed automatically
- Session expiration handling with automatic logout

#### Implementation
```javascript
// Login to <PERSON><PERSON>
const { access_token } = await loginToRexor(username, password);

// Check authentication status
const { isLoggedIn } = useRexor();
```

### 2. Project Registration

#### Process
- Users register projects with Rexor using Project ID
- Resource ID is automatically populated from user's economy system ID
- Project details are stored locally for transaction management

#### Implementation
```javascript
// Register a new project
const projectResponse = await registerProject({
  ProjectID: "PROJECT-123",
  ResourceID: "USER-456"
}, accessToken);
```

### 3. Transaction Management

#### Article Transactions
- Automatic creation of article expense transactions
- Support for both new transactions and updates
- Invoice text includes manual work estimation when available

#### Transaction Flow
1. AI response completion triggers automatic processing
2. Manual work estimation is generated in background
3. Transaction is created/updated with estimation included
4. Invoice text format: "Foynet number of lookups * Estimated manual time: X hours"

### 4. Manual Work Estimation Integration

#### Automatic Processing
The system automatically:
1. Detects when AI responses are completed
2. Extracts question/answer pairs from chat history
3. Generates manual work time estimates using LLM
4. Stores estimation results with thread/message association
5. Includes estimations in Rexor transactions

#### Storage Strategy
- Estimations stored in localStorage with keys: `manual_work_estimation_${threadSlug}_${messageId}`
- Automatic cleanup of estimations older than 24 hours
- Thread-based retrieval for transaction inclusion

#### Implementation
```javascript
// Automatic estimation on AI response completion
useAutoRexorIntegration(); // Hook handles everything automatically

// Manual estimation retrieval
const estimation = getLatestEstimationForThread(threadSlug);
if (estimation && estimation.totalHours > 0) {
  // Include in transaction
}
```

## Configuration

### System Settings

#### Required Settings
1. **Invoice Logging** (`invoice-logging`)
   - Enables automatic transaction creation
   - Must be enabled for Rexor integration to function

2. **Rexor Linkage** (`rexor-linkage`)
   - Enables Rexor API integration
   - Controls automatic transaction processing

#### Settings Management
```javascript
// Check settings
const invoiceLogging = getSetting("invoice-logging");
const rexorLinkage = getSetting("rexor-linkage");

// Both must be true for automatic processing
if (invoiceLogging && rexorLinkage) {
  // Process Rexor transactions
}
```

### User Configuration

#### Economy System ID
- Users must have `economy_system_id` set in their profile
- Used as ResourceID in Rexor project registration
- Required for transaction creation

#### Project Registration
- Users must register at least one project before transactions can be created
- Project details stored in localStorage: `rexor_registered_project`

## API Endpoints

### Frontend to Backend

#### Authentication
```
POST /api/system/rexor/auth
Content-Type: application/x-www-form-urlencoded

username=user&password=pass
```

#### Project Registration
```
POST /api/system/rexor/projects
Content-Type: application/json

{
  "access_token": "token",
  "projectData": {
    "ApiTable": "TProjectAll p",
    "ApiSelect": "...",
    "ApiJoin": "...",
    "ApiWhere": "ID = 'PROJECT-123'"
  }
}
```

#### Transaction Creation
```
POST /api/system/rexor/article-expense-transaction
Content-Type: application/json

{
  "access_token": "token",
  "transactionData": {
    "InvoiceText": "Foynet number of lookups * Estimated manual time: 2.5 hours",
    "Number": 1,
    "InvoicedNumber": 1,
    "Invoiceable": 1,
    "RegistrationDate": "2024-01-15",
    "Origin": 0,
    "InvoiceStatus": "ReadyForInvoiceBasis",
    "Status": 2
  }
}
```

#### Transaction Status
```
GET /api/system/rexor/transaction-status/{uid}
Authorization: Bearer {access_token}
```

### Backend to Rexor API

#### Base URL
```
https://api.rexor.se/v231
```

#### Authentication
```
POST https://auth.rexor.se/v231/Token
Content-Type: application/x-www-form-urlencoded

client_id=testfoyen&grant_type=password&username={user}&password={pass}
```

#### Project Operations
```
POST https://api.rexor.se/v231/Basic/Register
Authorization: Bearer {access_token}
Content-Type: application/json
```

#### Transaction Operations
```
POST https://api.rexor.se/v231/Project/ExpenseTransaction/Article
Authorization: Bearer {access_token}
Content-Type: application/json

GET https://api.rexor.se/v231/Project/ExpenseTransaction/ReadSingle/{uid}
Authorization: Bearer {access_token}
```

## Error Handling

### Authentication Errors
- 401 responses trigger automatic logout
- Users are prompted to re-authenticate
- Saved credentials allow quick re-login

### Transaction Errors
- Failed transactions are logged but don't block user workflow
- Fallback mechanisms ensure system continues functioning
- Error messages are internationalized

### Network Errors
- Graceful degradation when Rexor API is unavailable
- Local storage maintains state during outages
- Retry mechanisms for transient failures

## Internationalization

### Supported Languages
- English (en)
- Swedish (sv)
- French (fr)
- German (de)
- Norwegian (no)
- Polish (pl)
- Kinyarwanda (rw)

### Translation Keys
```javascript
// Main Rexor translations
"rexor.register-project": "Register Rexor Project"
"rexor.invoice-text": "Foynet number of lookups"
"rexor.estimated-manual-time": " * Estimated manual time: {{hours}} hours"

// Account management
"rexor.account.title": "Login to Rexor"
"rexor.account.username": "Username"
"rexor.account.password": "Password"
```

## Security Considerations

### Credential Storage
- Passwords are encrypted before localStorage storage
- Access tokens are stored securely
- Automatic cleanup on logout

### API Security
- All requests use HTTPS
- Bearer token authentication
- Proper error handling to prevent information leakage

### Data Privacy
- Minimal data sent to Rexor API
- User consent required for economy system integration
- Local storage can be cleared by users

## Testing

### Unit Tests
- Service layer functions (`rexorService.test.js`)
- Hook functionality (`useAutoRexorIntegration.test.js`)
- Utility functions (`manualWorkEstimation.test.js`)

### Test Coverage
- Authentication flows
- Transaction creation and updates
- Error handling scenarios
- Manual work estimation integration
- Settings validation

### Manual Testing
1. Login/logout flows
2. Project registration
3. Automatic transaction creation
4. Manual work estimation accuracy
5. Error recovery scenarios

## Future Enhancements

### Planned Improvements

#### LLM Integration Enhancement
```javascript
// TODO: Use the LLM set for validation prompts to run the estimation of time used
// instead of the current manual work estimation approach. This would be faster
// and less expensive LLM usage when systems are set up with more inexpensive LLM for validation,
// while still having the context window necessary for the validation prompt (and source checking for time estimation).
```

#### Additional Features
- Bulk transaction processing
- Advanced reporting integration
- Custom invoice templates
- Multi-project support
- Enhanced error recovery

### Performance Optimizations
- Caching of project data
- Batch transaction processing
- Optimized API calls
- Background sync capabilities

## Troubleshooting

### Common Issues

#### Authentication Problems
- **Issue**: Login fails with valid credentials
- **Solution**: Check network connectivity and Rexor API status
- **Prevention**: Implement retry logic and better error messages

#### Transaction Creation Failures
- **Issue**: Transactions fail to create
- **Solution**: Verify project registration and user economy ID
- **Prevention**: Validate prerequisites before transaction attempts

#### Manual Work Estimation Issues
- **Issue**: Estimations not appearing in transactions
- **Solution**: Check localStorage and thread slug extraction
- **Prevention**: Implement better error logging and fallback mechanisms

### Debug Information
- Enable console logging for detailed operation tracking
- Check localStorage for stored credentials and project data
- Verify system settings for invoice logging and Rexor linkage
- Monitor network requests for API communication issues

## Monitoring and Logging

### Client-Side Logging
- Authentication events
- Transaction creation/updates
- Manual work estimation results
- Error conditions and recovery

### Server-Side Logging
- API proxy requests
- Authentication token management
- Error responses from Rexor API
- Performance metrics

### Key Metrics
- Authentication success rate
- Transaction creation success rate
- Manual work estimation accuracy
- API response times
- Error frequency and types

## Conclusion

The Rexor economy API integration provides a comprehensive solution for automated invoice logging and project management within ISTLegal. The integration of manual work estimation enhances the value proposition by providing accurate time tracking for legal work, making the billing process more transparent and efficient.

The system is designed with reliability, security, and user experience in mind, ensuring seamless operation while maintaining data integrity and user privacy.
